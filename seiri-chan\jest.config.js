/** @type {import('jest').Config} */
const config = {
  // 使用 ts-jest 预设来处理 TypeScript
  preset: 'ts-jest',
  
  // 测试环境
  testEnvironment: 'node',
  
  // 根目录
  rootDir: '.',
  
  // 测试文件匹配模式
  testMatch: [
    '**/__tests__/**/*.test.ts',
    '**/__tests__/**/*.test.js'
  ],
  
  // 模块路径映射（对应 tsconfig.json 中的 paths）
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  
  // 覆盖率收集
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.{ts,js}',
    '!src/**/*.d.ts',
    '!src/**/*.test.ts',
    '!src/**/__tests__/**',
  ],
  
  // 覆盖率报告目录
  coverageDirectory: 'coverage',
  
  // 覆盖率报告格式
  coverageReporters: [
    'text',
    'lcov',
    'html'
  ],
  
  // 设置文件（在每个测试文件运行前执行）
  setupFilesAfterEnv: ['<rootDir>/__tests__/setup.ts'],
  
  // 忽略的路径
  testPathIgnorePatterns: [
    '/node_modules/',
    '/.next/',
    '/coverage/',
  ],
  
  // 转换忽略模式
  transformIgnorePatterns: [
    '/node_modules/(?!(smol-toml)/)',
  ],
  
  // 清除模拟
  clearMocks: true,
  
  // 详细输出
  verbose: true,
  
  // 超时设置
  testTimeout: 10000,
};

module.exports = config;
