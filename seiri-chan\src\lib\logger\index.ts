// 日志模块
import pino from 'pino';
import { join } from 'path';

// 日志级别类型
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// 创建日志器配置
const createLoggerConfig = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  // 基础配置
  const baseConfig: pino.LoggerOptions = {
    level: process.env.LOG_LEVEL || (isDevelopment ? 'debug' : 'info'),
    timestamp: pino.stdTimeFunctions.isoTime,
    formatters: {
      level: (label) => ({ level: label }),
    },
  };

  // 开发环境使用 pretty 格式
  if (isDevelopment) {
    return {
      ...baseConfig,
      transport: {
        target: 'pino-pretty',
        options: {
          colorize: true,
          translateTime: 'yyyy-mm-dd HH:MM:ss',
          ignore: 'pid,hostname',
          singleLine: false,
        },
      },
    };
  }

  // 生产环境使用 JSON 格式
  return baseConfig;
};

// 创建主日志器
export const logger = pino(createLoggerConfig());

// 创建文件日志器（用于持久化日志）
export const createFileLogger = (filename: string) => {
  const logDir = join(process.cwd(), 'logs');
  const logPath = join(logDir, filename);
  
  return pino(
    {
      level: 'info',
      timestamp: pino.stdTimeFunctions.isoTime,
    },
    pino.destination({
      dest: logPath,
      sync: false,
      mkdir: true,
    })
  );
};

// 任务日志器（专门用于任务处理日志）
export const taskLogger = createFileLogger('tasks.log');

// 错误日志器（专门用于错误日志）
export const errorLogger = createFileLogger('errors.log');

// API 日志器（专门用于 API 请求日志）
export const apiLogger = createFileLogger('api.log');

// 日志工具函数
export const logUtils = {
  /**
   * 记录任务开始
   */
  logTaskStart: (taskId: string, taskType: string, sourcePath: string) => {
    taskLogger.info({
      event: 'task_start',
      taskId,
      taskType,
      sourcePath,
    }, `任务开始: ${taskType} - ${sourcePath}`);
  },

  /**
   * 记录任务完成
   */
  logTaskComplete: (taskId: string, taskType: string, duration: number, fileCount: number) => {
    taskLogger.info({
      event: 'task_complete',
      taskId,
      taskType,
      duration,
      fileCount,
    }, `任务完成: ${taskType} - 处理 ${fileCount} 个文件，耗时 ${duration}ms`);
  },

  /**
   * 记录任务失败
   */
  logTaskError: (taskId: string, taskType: string, error: Error | string) => {
    const errorMessage = error instanceof Error ? error.message : error;
    const errorStack = error instanceof Error ? error.stack : undefined;
    
    taskLogger.error({
      event: 'task_error',
      taskId,
      taskType,
      error: errorMessage,
      stack: errorStack,
    }, `任务失败: ${taskType} - ${errorMessage}`);
    
    // 同时记录到错误日志
    errorLogger.error({
      event: 'task_error',
      taskId,
      taskType,
      error: errorMessage,
      stack: errorStack,
    }, `任务失败: ${taskType} - ${errorMessage}`);
  },

  /**
   * 记录文件操作
   */
  logFileOperation: (
    taskId: string, 
    operation: string, 
    sourcePath: string, 
    targetPath: string, 
    success: boolean,
    error?: string
  ) => {
    const logData = {
      event: 'file_operation',
      taskId,
      operation,
      sourcePath,
      targetPath,
      success,
      error,
    };

    if (success) {
      taskLogger.info(logData, `文件操作成功: ${operation} ${sourcePath} -> ${targetPath}`);
    } else {
      taskLogger.error(logData, `文件操作失败: ${operation} ${sourcePath} -> ${targetPath} - ${error}`);
    }
  },

  /**
   * 记录 API 请求
   */
  logApiRequest: (method: string, url: string, statusCode: number, duration: number, userAgent?: string) => {
    apiLogger.info({
      event: 'api_request',
      method,
      url,
      statusCode,
      duration,
      userAgent,
    }, `${method} ${url} - ${statusCode} (${duration}ms)`);
  },

  /**
   * 记录 API 错误
   */
  logApiError: (method: string, url: string, error: Error | string, userAgent?: string) => {
    const errorMessage = error instanceof Error ? error.message : error;
    const errorStack = error instanceof Error ? error.stack : undefined;
    
    apiLogger.error({
      event: 'api_error',
      method,
      url,
      error: errorMessage,
      stack: errorStack,
      userAgent,
    }, `API 错误: ${method} ${url} - ${errorMessage}`);
  },
};

// 导出默认日志器
export default logger;
