// 错误处理模块测试
import {
  AppError,
  ConfigError,
  FileOperationError,
  TaskError,
  NetworkError,
  DatabaseError,
  MediaAnalysisError,
  ErrorCode,
  ErrorSeverity,
  errorUtils,
  withErrorHandling,
} from '@/lib/errors';

describe('错误处理模块', () => {
  describe('AppError', () => {
    it('应该正确创建基础错误', () => {
      const error = new AppError(
        '测试错误',
        ErrorCode.VALIDATION_ERROR,
        ErrorSeverity.MEDIUM,
        { testData: 'test' }
      );

      expect(error.message).toBe('测试错误');
      expect(error.code).toBe(ErrorCode.VALIDATION_ERROR);
      expect(error.severity).toBe(ErrorSeverity.MEDIUM);
      expect(error.context).toEqual({ testData: 'test' });
      expect(error.timestamp).toBeInstanceOf(Date);
      expect(error.name).toBe('AppError');
    });

    it('应该使用默认值', () => {
      const error = new AppError('测试错误');

      expect(error.code).toBe(ErrorCode.UNKNOWN_ERROR);
      expect(error.severity).toBe(ErrorSeverity.MEDIUM);
      expect(error.context).toBeUndefined();
    });

    it('应该正确序列化为 JSON', () => {
      const error = new AppError(
        '测试错误',
        ErrorCode.VALIDATION_ERROR,
        ErrorSeverity.HIGH,
        { testData: 'test' }
      );

      const json = error.toJSON();

      expect(json.name).toBe('AppError');
      expect(json.message).toBe('测试错误');
      expect(json.code).toBe(ErrorCode.VALIDATION_ERROR);
      expect(json.severity).toBe(ErrorSeverity.HIGH);
      expect(json.context).toEqual({ testData: 'test' });
      expect(json.timestamp).toBeDefined();
      expect(json.stack).toBeDefined();
    });
  });

  describe('特定错误类型', () => {
    it('ConfigError 应该正确设置属性', () => {
      const error = new ConfigError('配置错误', { configPath: '/test' });

      expect(error.name).toBe('ConfigError');
      expect(error.code).toBe(ErrorCode.CONFIG_LOAD_ERROR);
      expect(error.severity).toBe(ErrorSeverity.HIGH);
      expect(error.context).toEqual({ configPath: '/test' });
    });

    it('FileOperationError 应该正确设置属性', () => {
      const error = new FileOperationError(
        '文件操作失败',
        ErrorCode.FILE_NOT_FOUND,
        { filePath: '/test/file' }
      );

      expect(error.name).toBe('FileOperationError');
      expect(error.code).toBe(ErrorCode.FILE_NOT_FOUND);
      expect(error.severity).toBe(ErrorSeverity.MEDIUM);
    });

    it('TaskError 应该正确设置属性', () => {
      const error = new TaskError(
        '任务失败',
        ErrorCode.TASK_EXECUTION_FAILED,
        { taskId: 'task-123' }
      );

      expect(error.name).toBe('TaskError');
      expect(error.code).toBe(ErrorCode.TASK_EXECUTION_FAILED);
    });

    it('NetworkError 应该正确设置属性', () => {
      const error = new NetworkError(
        '网络错误',
        ErrorCode.API_ERROR,
        { url: 'https://api.test.com' }
      );

      expect(error.name).toBe('NetworkError');
      expect(error.code).toBe(ErrorCode.API_ERROR);
    });

    it('DatabaseError 应该正确设置属性', () => {
      const error = new DatabaseError('数据库错误', { query: 'SELECT *' });

      expect(error.name).toBe('DatabaseError');
      expect(error.code).toBe(ErrorCode.DATABASE_ERROR);
      expect(error.severity).toBe(ErrorSeverity.HIGH);
    });

    it('MediaAnalysisError 应该正确设置属性', () => {
      const error = new MediaAnalysisError('媒体分析失败', { mediaPath: '/test/media' });

      expect(error.name).toBe('MediaAnalysisError');
      expect(error.code).toBe(ErrorCode.MEDIA_ANALYSIS_FAILED);
    });
  });

  describe('errorUtils', () => {
    describe('createErrorResponse', () => {
      it('应该为 AppError 创建正确的响应', () => {
        const error = new AppError(
          '测试错误',
          ErrorCode.VALIDATION_ERROR,
          ErrorSeverity.HIGH,
          { testData: 'test' }
        );

        const response = errorUtils.createErrorResponse(error, 'req-123');

        expect(response.success).toBe(false);
        expect(response.error.message).toBe('测试错误');
        expect(response.error.code).toBe(ErrorCode.VALIDATION_ERROR);
        expect(response.error.severity).toBe(ErrorSeverity.HIGH);
        expect(response.error.context).toEqual({ testData: 'test' });
        expect(response.error.requestId).toBe('req-123');
      });

      it('应该为普通 Error 创建正确的响应', () => {
        const error = new Error('普通错误');

        const response = errorUtils.createErrorResponse(error);

        expect(response.success).toBe(false);
        expect(response.error.message).toBe('普通错误');
        expect(response.error.code).toBe(ErrorCode.UNKNOWN_ERROR);
        expect(response.error.severity).toBe(ErrorSeverity.MEDIUM);
      });
    });

    describe('wrapAsync', () => {
      it('应该正常执行成功的异步函数', async () => {
        const successFn = jest.fn().mockResolvedValue('success');
        const wrappedFn = errorUtils.wrapAsync(successFn);

        const result = await wrappedFn('arg1', 'arg2');

        expect(result).toBe('success');
        expect(successFn).toHaveBeenCalledWith('arg1', 'arg2');
      });

      it('应该将普通错误包装为 AppError', async () => {
        const failFn = jest.fn().mockRejectedValue(new Error('原始错误'));
        const wrappedFn = errorUtils.wrapAsync(
          failFn,
          ErrorCode.TASK_EXECUTION_FAILED,
          ErrorSeverity.HIGH
        );

        await expect(wrappedFn()).rejects.toThrow(AppError);
        
        try {
          await wrappedFn();
        } catch (error) {
          expect(error).toBeInstanceOf(AppError);
          expect((error as AppError).code).toBe(ErrorCode.TASK_EXECUTION_FAILED);
          expect((error as AppError).severity).toBe(ErrorSeverity.HIGH);
          expect((error as AppError).message).toBe('原始错误');
        }
      });

      it('应该直接抛出 AppError', async () => {
        const appError = new AppError('应用错误', ErrorCode.VALIDATION_ERROR);
        const failFn = jest.fn().mockRejectedValue(appError);
        const wrappedFn = errorUtils.wrapAsync(failFn);

        await expect(wrappedFn()).rejects.toBe(appError);
      });
    });

    describe('fromNativeError', () => {
      it('应该从原生错误创建 AppError', () => {
        const nativeError = new Error('原生错误');
        nativeError.stack = 'test stack';

        const appError = errorUtils.fromNativeError(
          nativeError,
          ErrorCode.FILE_OPERATION_FAILED,
          ErrorSeverity.HIGH,
          { source: 'test' }
        );

        expect(appError).toBeInstanceOf(AppError);
        expect(appError.message).toBe('原生错误');
        expect(appError.code).toBe(ErrorCode.FILE_OPERATION_FAILED);
        expect(appError.severity).toBe(ErrorSeverity.HIGH);
        expect(appError.context).toEqual({
          source: 'test',
          originalStack: 'test stack',
        });
      });
    });

    describe('isErrorCode', () => {
      it('应该正确检查错误代码', () => {
        const appError = new AppError('测试', ErrorCode.VALIDATION_ERROR);
        const normalError = new Error('普通错误');

        expect(errorUtils.isErrorCode(appError, ErrorCode.VALIDATION_ERROR)).toBe(true);
        expect(errorUtils.isErrorCode(appError, ErrorCode.UNKNOWN_ERROR)).toBe(false);
        expect(errorUtils.isErrorCode(normalError, ErrorCode.VALIDATION_ERROR)).toBe(false);
      });
    });

    describe('isErrorSeverity', () => {
      it('应该正确检查错误严重级别', () => {
        const appError = new AppError('测试', ErrorCode.VALIDATION_ERROR, ErrorSeverity.HIGH);
        const normalError = new Error('普通错误');

        expect(errorUtils.isErrorSeverity(appError, ErrorSeverity.HIGH)).toBe(true);
        expect(errorUtils.isErrorSeverity(appError, ErrorSeverity.LOW)).toBe(false);
        expect(errorUtils.isErrorSeverity(normalError, ErrorSeverity.HIGH)).toBe(false);
      });
    });
  });

  describe('withErrorHandling', () => {
    it('应该正常处理成功的请求', async () => {
      const mockHandler = jest.fn().mockResolvedValue('success');
      const wrappedHandler = withErrorHandling(mockHandler);
      
      const mockReq = { method: 'GET', url: '/test' };
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };

      await wrappedHandler(mockReq, mockRes);

      expect(mockHandler).toHaveBeenCalledWith(mockReq, mockRes);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('应该处理 AppError', async () => {
      const appError = new AppError(
        'API 错误',
        ErrorCode.VALIDATION_ERROR,
        ErrorSeverity.MEDIUM
      );
      const mockHandler = jest.fn().mockRejectedValue(appError);
      const wrappedHandler = withErrorHandling(mockHandler);
      
      const mockReq = { 
        method: 'POST', 
        url: '/test',
        headers: { 'user-agent': 'test-agent' }
      };
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };

      await wrappedHandler(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: expect.objectContaining({
            message: 'API 错误',
            code: ErrorCode.VALIDATION_ERROR,
            severity: ErrorSeverity.MEDIUM,
          }),
        })
      );
    });

    it('应该处理严重错误', async () => {
      const criticalError = new AppError(
        '严重错误',
        ErrorCode.DATABASE_ERROR,
        ErrorSeverity.CRITICAL
      );
      const mockHandler = jest.fn().mockRejectedValue(criticalError);
      const wrappedHandler = withErrorHandling(mockHandler);
      
      const mockReq = { method: 'GET', url: '/test', headers: {} };
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };

      await wrappedHandler(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });
});
