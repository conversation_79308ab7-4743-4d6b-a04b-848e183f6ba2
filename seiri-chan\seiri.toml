[general]
[general.tmdb]
apiKey = "eyJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************.6YLHD9pUk6M0Eh3MZSCJWKOOikEaDnbVek0C5uWAkJc"
language = "zh-CN"
region = "CN"

[general.paths]
outputRoot = "/tmp/seiri-output"

[general.defaults]
fileOperation = "copy"
mediaType = "anime"
enableAI = false

[ai]
provider = "openai"

[ai.openai]
model = "gpt-4o-mini"
temperature = 0.1
maxTokens = 4000

[ai.gemini]
model = "gemini-1.5-flash"
temperature = 0.1
maxTokens = 4000

[ai.claude]
model = "claude-3-haiku-20240307"
temperature = 0.1
maxTokens = 4000

[ai.analysis]
confidenceThreshold = 0.8
enableMovieSeparation = true

[other]
[other.logging]
level = "info"
enableFileLogging = true
maxLogFiles = 10

[other.performance]
maxConcurrentFiles = 5
maxQueueLength = 100

[other.experimental]
enableSubtitleOrganization = false
enableTrailerOrganization = false
enableFontCollection = false