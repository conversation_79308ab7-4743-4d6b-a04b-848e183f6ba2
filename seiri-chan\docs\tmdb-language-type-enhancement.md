# TMDB 语言类型安全增强

## 概述

本次更新集成了 tmdb-ts 库的 `AvailableLanguage` 类型，为 TMDB 客户端提供了严格的语言类型验证，提升了类型安全性和开发体验。

## 主要改进

### 1. 类型导入和集成

```typescript
// 从 tmdb-ts 导入严格的语言类型
import type { AvailableLanguage } from 'tmdb-ts';

// 在配置类型中使用
export interface TmdbClientConfig {
  apiKey: string;
  language?: AvailableLanguage;  // 严格的语言类型
  region?: string;
  baseURL?: string;
}
```

### 2. 配置验证增强

```typescript
// 创建 Zod 枚举验证器
const TMDB_LANGUAGES = [
  'af-ZA', 'ar-AE', 'ar-BH', 'ar-EG', 'ar-IQ', 'ar-JO', 'ar-LY', 'ar-MA', 
  'ar-QA', 'ar-SA', 'ar-TD', 'ar-YE', 'be-BY', 'bg-BG', 'bn-BD', 'br-FR',
  // ... 125+ 种语言
  'zh-CN', 'zh-HK', 'zh-SG', 'zh-TW', 'zu-ZA'
] as const;

export const TmdbLanguageSchema = z.enum(TMDB_LANGUAGES);
export type TmdbLanguage = z.infer<typeof TmdbLanguageSchema>;

// 在配置 Schema 中使用
tmdb: z.object({
  apiKey: z.string().min(1, 'TMDB API Key 不能为空'),
  language: TmdbLanguageSchema.default('zh-CN'),  // 严格验证
  region: z.string().default('CN'),
}),
```

### 3. API 调用优化

移除了不必要的 `as any` 类型转换：

```typescript
// 之前
const response = await this.tmdb.search.multi({
  query,
  page,
  language: this.config.language as any,  // 不安全的类型转换
});

// 现在
const response = await this.tmdb.search.multi({
  query,
  page,
  language: this.config.language,  // 类型安全
});
```

### 4. 季详情 API 调用修复

```typescript
// 修复了季详情获取的 API 调用
const response = await this.tmdb.tvSeasons.details(
  {
    tvShowID: tvId,
    seasonNumber: seasonNumber 
  } as SeasonSelection,
  undefined,
  {
    language: this.config.language,  // 使用严格类型
  }
);
```

## 支持的语言

### 主要语言
- **中文**: `zh-CN` (简体中文), `zh-HK` (香港), `zh-SG` (新加坡), `zh-TW` (台湾)
- **英文**: `en-US` (美国), `en-GB` (英国), `en-CA` (加拿大), `en-AU` (澳大利亚)
- **日文**: `ja-JP`
- **韩文**: `ko-KR`
- **法文**: `fr-FR` (法国), `fr-CA` (加拿大)
- **德文**: `de-DE` (德国), `de-AT` (奥地利), `de-CH` (瑞士)
- **西班牙文**: `es-ES` (西班牙), `es-MX` (墨西哥), `es-AR` (阿根廷)

### 其他语言
总计支持 125+ 种语言和地区组合，覆盖全球主要市场。

## 开发体验改进

### 1. 编译时类型检查
```typescript
// ✅ 有效的语言代码
const config = { language: 'zh-CN' as const };

// ❌ 无效的语言代码 - TypeScript 编译错误
const invalidConfig = { language: 'invalid-lang' };
```

### 2. IDE 自动补全
IDE 现在可以为 TMDB 语言配置提供智能补全，显示所有可用的语言选项。

### 3. 运行时验证
```typescript
// 配置加载时自动验证
const validatedConfig = ConfigSchema.parse(rawConfig);
// 如果语言代码无效，会抛出详细的错误信息
```

## 测试更新

更新了测试用例以使用严格的语言类型：

```typescript
// 测试中使用 const 断言确保类型安全
const newConfig = {
  language: 'en-US' as const,  // 严格类型
  region: 'US',
};
```

## 向后兼容性

- 现有配置文件中的有效语言代码继续工作
- 默认语言保持为 `zh-CN`
- 无效的语言代码会在配置加载时被捕获并报告

## 文件变更

### 修改的文件
- `src/lib/tmdb/types.ts` - 添加 AvailableLanguage 导入
- `src/lib/tmdb/client.ts` - 移除类型转换，修复季详情 API
- `src/lib/config/types.ts` - 添加严格的语言验证
- `__tests__/lib/tmdb/client.test.ts` - 更新测试用例

### 更新的文档
- `docs/unit-2.1-tmdb-client-cache.md` - 添加语言类型验证说明
- `docs/tmdb-language-type-enhancement.md` - 本文档

## 总结

这次更新显著提升了 TMDB 模块的类型安全性：

1. **编译时验证**: 防止无效语言代码进入生产环境
2. **更好的开发体验**: IDE 自动补全和错误提示
3. **运行时安全**: 配置验证捕获无效值
4. **API 兼容性**: 与 TMDB API 完全兼容
5. **测试覆盖**: 所有变更都有相应的测试用例

---

**完成时间**: 2025-08-02  
**测试状态**: ✅ 全部通过 (21 个测试用例)  
**类型安全**: ✅ 完全类型安全，无 any 类型
