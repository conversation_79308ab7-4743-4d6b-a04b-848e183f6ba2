# 单元 1.3: 配置管理模块 (config.ts) - 完成文档

## 概述
本单元完成了 Seiri-chan 项目的配置管理系统，实现了基于 TOML 格式的配置文件管理，提供类型安全的配置读取、更新和验证功能。

## 完成的工作

### 1. 配置类型定义 (`src/lib/config/types.ts`)
使用 Zod 定义了完整的配置 Schema，包括：

#### 核心枚举类型
- `FileOperation`: 文件操作类型 (hardlink, softlink, copy, move, skip)
- `MediaType`: 媒体类型 (anime, tv, movie, anime_movie)
- `AIProvider`: AI 提供商 (openai, gemini, claude)

#### 配置结构
- **GeneralConfig**: 通用配置
  - TMDB 配置 (API Key, 语言, 地区)
  - 路径配置 (输出根目录, 临时目录)
  - 默认设置 (文件操作, 媒体类型, AI 启用状态)

- **AIConfig**: AI 配置
  - 提供商选择和各提供商的详细配置
  - OpenA<PERSON>, <PERSON>, Claude 的 API 配置
  - AI 分析配置 (置信度阈值, 电影分离, 自定义 Prompt)

- **OtherConfig**: 其他配置
  - 日志配置 (级别, 文件日志, 最大文件数)
  - 性能配置 (并发数, 队列长度)
  - 实验性功能开关

### 2. 配置管理器 (`src/lib/config/manager.ts`)
实现了单例模式的配置管理器，提供：

#### 核心功能
- **配置加载**: 从 `seiri.toml` 文件加载配置
- **配置验证**: 使用 Zod Schema 进行严格验证
- **配置更新**: 支持部分配置更新和深度合并
- **配置保存**: 将配置保存为 TOML 格式
- **默认配置**: 当配置文件不存在时使用默认配置

#### 错误处理
- 配置文件不存在时自动使用默认配置
- 配置格式错误时回退到默认配置
- 详细的错误日志记录

#### 配置合并
- 深度合并算法，确保新配置与默认配置正确合并
- 保持配置结构的完整性

### 3. 配置模块入口 (`src/lib/config/index.ts`)
提供便捷的配置访问函数：
- `getConfig()`: 获取完整配置
- `getGeneralConfig()`: 获取通用配置
- `getAIConfig()`: 获取 AI 配置
- `getOtherConfig()`: 获取其他配置
- `updateConfig()`: 更新配置
- `reloadConfig()`: 重新加载配置
- `configExists()`: 检查配置文件是否存在
- `createDefaultConfig()`: 创建默认配置文件
- `validateConfig()`: 验证配置

### 4. 示例配置文件 (`seiri.toml.example`)
创建了详细的配置文件示例，包含：
- 所有配置项的说明和示例值
- 必填和可选配置的标注
- 各个 AI 提供商的配置示例

### 5. 配置测试 (`src/lib/config/test.ts`)
实现了配置管理器的测试脚本：
- 测试配置文件的创建和加载
- 测试配置更新功能
- 验证配置管理器的基本功能

## 技术特性

### 类型安全
- 使用 TypeScript 和 Zod 提供完整的类型安全
- 编译时和运行时的双重类型检查
- 自动类型推导和智能提示

### 配置验证
- 严格的 Schema 验证，确保配置格式正确
- 必填字段验证和默认值处理
- 数值范围和格式验证

### 错误恢复
- 配置文件损坏时自动回退到默认配置
- 详细的错误日志，便于问题诊断
- 优雅的错误处理，不会导致应用崩溃

### 单例模式
- 确保全局只有一个配置管理器实例
- 避免重复加载配置文件
- 提供一致的配置访问接口

## 配置文件结构
```toml
[general]
[general.tmdb]
apiKey = "your_tmdb_api_key"
language = "zh-CN"
region = "CN"

[general.paths]
outputRoot = "/path/to/media/library"
tempDir = "/path/to/temp"

[general.defaults]
fileOperation = "copy"
mediaType = "anime"
enableAI = false

[ai]
provider = "openai"

[ai.openai]
apiKey = "your_openai_api_key"
model = "gpt-4o-mini"
temperature = 0.1
maxTokens = 4000

# ... 其他配置项
```

## 使用示例
```typescript
import { getConfig, updateConfig } from '@/lib/config';

// 获取配置
const config = getConfig();
console.log(config.general.tmdb.apiKey);

// 更新配置
await updateConfig({
  general: {
    defaults: {
      enableAI: true,
    },
  },
});
```

## 下一步
- 单元 1.4: 日志与错误处理模块

## 文件位置
- 类型定义: `src/lib/config/types.ts`
- 配置管理器: `src/lib/config/manager.ts`
- 模块入口: `src/lib/config/index.ts`
- 示例配置: `seiri.toml.example`
- 实际配置: `seiri.toml`
- 测试脚本: `src/lib/config/test.ts`
