// TMDB 模块入口
export * from './types';
export * from './client';
export * from './cache';

import { TmdbClient } from './client';
import { TmdbClientConfig } from './types';
import { PrismaClient } from '@prisma/client';
import { getConfig } from '../config';
import { logger } from '../logger';

// 全局 TMDB 客户端实例
let tmdbClientInstance: TmdbClient | null = null;

/**
 * 获取 TMDB 客户端实例（单例模式）
 */
export function getTmdbClient(prisma?: PrismaClient): TmdbClient {
  if (!tmdbClientInstance) {
    if (!prisma) {
      throw new Error('首次获取 TMDB 客户端时必须提供 Prisma 实例');
    }

    const config = getConfig();
    const tmdbConfig: TmdbClientConfig = {
      apiKey: config.general.tmdb.apiKey,
      language: config.general.tmdb.language,
      region: config.general.tmdb.region,
    };

    tmdbClientInstance = new TmdbClient(tmdbConfig, prisma);
    logger.info('TMDB 客户端单例已创建');
  }

  return tmdbClientInstance;
}

/**
 * 重置 TMDB 客户端实例（用于配置更新）
 */
export function resetTmdbClient(): void {
  tmdbClientInstance = null;
  logger.info('TMDB 客户端单例已重置');
}

/**
 * 更新 TMDB 客户端配置
 */
export function updateTmdbClientConfig(config: Partial<TmdbClientConfig>): void {
  if (tmdbClientInstance) {
    tmdbClientInstance.updateConfig(config);
  }
}

/**
 * 清除所有 TMDB 缓存
 */
export async function clearAllTmdbCache(): Promise<{ searchCount: number; mediaCount: number; seasonCount: number }> {
  if (!tmdbClientInstance) {
    throw new Error('TMDB 客户端未初始化，请先调用 getTmdbClient()');
  }
  return await tmdbClientInstance.clearAllCache();
}

/**
 * 清除所有搜索缓存
 */
export async function clearTmdbSearchCache(): Promise<number> {
  if (!tmdbClientInstance) {
    throw new Error('TMDB 客户端未初始化，请先调用 getTmdbClient()');
  }
  return await tmdbClientInstance.clearSearchCache();
}

/**
 * 清除所有媒体缓存
 */
export async function clearTmdbMediaCache(): Promise<number> {
  if (!tmdbClientInstance) {
    throw new Error('TMDB 客户端未初始化，请先调用 getTmdbClient()');
  }
  return await tmdbClientInstance.clearMediaCache();
}

/**
 * 清除所有季缓存
 */
export async function clearTmdbSeasonCache(): Promise<number> {
  if (!tmdbClientInstance) {
    throw new Error('TMDB 客户端未初始化，请先调用 getTmdbClient()');
  }
  return await tmdbClientInstance.clearSeasonCache();
}

/**
 * 清理过期缓存
 */
export async function cleanupTmdbCache(): Promise<void> {
  if (!tmdbClientInstance) {
    throw new Error('TMDB 客户端未初始化，请先调用 getTmdbClient()');
  }
  return await tmdbClientInstance.cleanupCache();
}
