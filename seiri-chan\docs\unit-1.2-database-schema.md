# 单元 1.2: 数据库 Schema 定义 (Prisma) - 完成文档

## 概述
本单元完成了 Seiri-chan 项目的数据库 Schema 定义，使用 Prisma ORM 创建了完整的数据模型，包括任务管理、文件处理和 TMDB 缓存系统。

## 完成的工作

### 1. Prisma 初始化
- 创建了 `prisma/schema.prisma` 文件
- 配置了 SQLite 数据源：`file:./seiri.db`
- 设置了 Prisma Client 生成器

### 2. 枚举定义
根据 DESIGN.md 定义了以下类型安全的枚举：

#### MediaTaskStatus (媒体任务状态)
- `PENDING` - 任务已创建，等待处理
- `SCANNING` - 正在扫描文件和获取元数据
- `ANALYSIS_FAILED` - 分析失败（如找不到匹配媒体）
- `ANALYSIS_AWAITING_USER` - 分析完成，但存在需要用户确认的情况
- `PROCESSING` - 正在执行文件操作
- `COMPLETED` - 所有文件任务成功
- `CONFIRMATION_REQUIRED` - 部分文件任务成功，个别需要用户确认
- `FAILED` - 任务因可恢复错误失败
- `FATAL_ERROR` - 移动模式下出现严重错误

#### FileTaskStatus (文件任务状态)
- `PENDING` - 等待分析
- `IGNORED` - 未命中匹配规则或不在AI识别结果中，不处理
- `MAPPED` - 已成功映射到目标，等待处理
- `MISSING` - AI给出了不存在的源文件路径
- `PROCESSING` - 文件复制或移动中
- `COMPLETED` - 整理成功
- `FILE_NOT_EXIST` - 源文件在处理时已不存在
- `FAILED` - 文件整理失败
- `FATAL_ERROR` - 移动模式出现严重错误
- `CONFIRMATION_REQUIRED` - AI置信度低或存在冲突，等待用户确认

#### FileType (文件类型)
- `VIDEO_MAIN` - 正片视频
- `VIDEO_SPECIAL` - 特典视频 (SP, OVA)
- `VIDEO_TRAILER` - 预告片 (Trailer, PV)
- `VIDEO_EXTRA` - OP, ED 等
- `VIDEO_UNKNOWN` - 未知类型视频
- `SUBTITLE` - 字幕文件
- `FONT` - 字体文件

#### MediaType (媒体类型)
- `ANIME` - 动画
- `TV` - 电视剧
- `MOVIE` - 电影
- `ANIME_MOVIE` - 动画电影

#### FileOperation (文件操作)
- `HARDLINK` - 硬链接
- `SOFTLINK` - 软链接
- `COPY` - 复制
- `MOVE` - 移动
- `SKIP` - 跳过（用于试运行）

### 3. 核心数据模型

#### MediaTask (媒体任务)
顶层任务模型，包含：
- 基本信息：ID、源路径、创建时间、完成时间
- 状态管理：任务状态、媒体类型、文件操作类型
- TMDB 元数据：TMDB ID、显示名称、年份、海报路径
- 关联关系：一对多关联到 FileTask

#### FileTask (文件任务)
单个文件的处理记录，包含：
- 文件信息：源路径、关联文件、文件大小、时长、分辨率
- 处理状态：任务状态、文件类型、文件操作
- 目标信息：目标季号、集号、目标路径
- 错误处理：结构化错误信息存储
- 复合唯一键：(mediaTaskId, sourcePath)

### 4. TMDB 缓存系统

#### TmdbSearchCache (搜索缓存)
- 以搜索关键词为主键
- 存储完整的搜索结果 JSON
- 包含创建时间戳

#### TmdbMediaCache (媒体缓存)
- 以 TMDB ID 为主键
- 区分电影和电视剧类型
- 存储完整的媒体元数据 JSON

#### TmdbSeasonCache (季数据缓存)
- 复合主键：(tvId, seasonNumber)
- 存储完整的季数据 JSON
- 支持电视剧的分季缓存

### 5. 数据库创建与验证
- 成功生成 Prisma Client
- 创建了 SQLite 数据库文件 `seiri.db`
- 运行了数据库同步 (`prisma db push`)
- 验证了所有模型和关系的正确性

## 技术特性
- **类型安全**: 所有枚举和模型都提供完整的 TypeScript 类型支持
- **关系完整性**: 正确定义了外键关系和级联操作
- **缓存优化**: 设计了高效的 TMDB 数据缓存策略
- **错误处理**: 支持结构化错误信息存储
- **扩展性**: 模型设计支持未来功能扩展

## 下一步
- 单元 1.3: 配置管理模块 (config.ts)
- 单元 1.4: 日志与错误处理模块

## 文件位置
- Schema 定义: `prisma/schema.prisma`
- 数据库文件: `prisma/seiri.db`
- 生成的客户端: `node_modules/@prisma/client`
