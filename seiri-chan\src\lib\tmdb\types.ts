// TMDB 相关类型定义
import { z } from 'zod';
import type {
  AvailableLanguage,
  MovieDetails,
  TvShowDetails,
  SeasonDetails
} from 'tmdb-ts';

// TMDB 媒体类型
export enum TmdbMediaType {
  MOVIE = 'movie',
  TV = 'tv'
}

// TMDB 搜索结果项
export const TmdbSearchResultItemSchema = z.object({
  id: z.number(),
  media_type: z.enum(['movie', 'tv']).optional(),
  title: z.string().optional(), // 电影标题
  name: z.string().optional(),  // 电视剧名称
  original_title: z.string().optional(),
  original_name: z.string().optional(),
  overview: z.string().optional(),
  poster_path: z.string().nullable().optional(),
  backdrop_path: z.string().nullable().optional(),
  release_date: z.string().optional(), // 电影发布日期
  first_air_date: z.string().optional(), // 电视剧首播日期
  vote_average: z.number().optional(),
  vote_count: z.number().optional(),
  popularity: z.number().optional(),
  genre_ids: z.array(z.number()).optional(),
  adult: z.boolean().optional(),
  video: z.boolean().optional(), // 仅电影
  origin_country: z.array(z.string()).optional(), // 仅电视剧
  original_language: z.string().optional(),
});

export type TmdbSearchResultItem = z.infer<typeof TmdbSearchResultItemSchema>;

// TMDB 搜索响应
export const TmdbSearchResponseSchema = z.object({
  page: z.number(),
  results: z.array(TmdbSearchResultItemSchema),
  total_pages: z.number(),
  total_results: z.number(),
});

export type TmdbSearchResponse = z.infer<typeof TmdbSearchResponseSchema>;

// 重新导出 tmdb-ts 中的电影详情类型
export type TmdbMovieDetails = MovieDetails;

// 重新导出 tmdb-ts 中的电视剧详情类型
export type TmdbTvDetails = TvShowDetails;

// 重新导出 tmdb-ts 中的季详情类型
export type TmdbSeasonDetails = SeasonDetails;

// 缓存键类型
export interface TmdbCacheKey {
  search: string;
  media: { id: number; type: TmdbMediaType };
  season: { tvId: number; seasonNumber: number };
}

// 错误类型
export class TmdbError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'TmdbError';
  }
}

// TMDB 客户端配置
export interface TmdbClientConfig {
  apiKey: string;
  language?: AvailableLanguage;
  region?: string;
  baseURL?: string;
}
