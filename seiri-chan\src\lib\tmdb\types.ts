// TMDB 相关类型定义
import { z } from 'zod';
import type { AvailableLanguage } from 'tmdb-ts';

// TMDB 媒体类型
export enum TmdbMediaType {
  MOVIE = 'movie',
  TV = 'tv'
}

// TMDB 搜索结果项
export const TmdbSearchResultItemSchema = z.object({
  id: z.number(),
  media_type: z.enum(['movie', 'tv']).optional(),
  title: z.string().optional(), // 电影标题
  name: z.string().optional(),  // 电视剧名称
  original_title: z.string().optional(),
  original_name: z.string().optional(),
  overview: z.string().optional(),
  poster_path: z.string().nullable().optional(),
  backdrop_path: z.string().nullable().optional(),
  release_date: z.string().optional(), // 电影发布日期
  first_air_date: z.string().optional(), // 电视剧首播日期
  vote_average: z.number().optional(),
  vote_count: z.number().optional(),
  popularity: z.number().optional(),
  genre_ids: z.array(z.number()).optional(),
  adult: z.boolean().optional(),
  video: z.boolean().optional(), // 仅电影
  origin_country: z.array(z.string()).optional(), // 仅电视剧
  original_language: z.string().optional(),
});

export type TmdbSearchResultItem = z.infer<typeof TmdbSearchResultItemSchema>;

// TMDB 搜索响应
export const TmdbSearchResponseSchema = z.object({
  page: z.number(),
  results: z.array(TmdbSearchResultItemSchema),
  total_pages: z.number(),
  total_results: z.number(),
});

export type TmdbSearchResponse = z.infer<typeof TmdbSearchResponseSchema>;

// TMDB 电影详情
export const TmdbMovieDetailsSchema = z.object({
  id: z.number(),
  title: z.string(),
  original_title: z.string(),
  overview: z.string().nullable(),
  poster_path: z.string().nullable(),
  backdrop_path: z.string().nullable(),
  release_date: z.string(),
  runtime: z.number().nullable(),
  vote_average: z.number(),
  vote_count: z.number(),
  popularity: z.number(),
  adult: z.boolean(),
  video: z.boolean(),
  original_language: z.string(),
  genres: z.array(z.object({
    id: z.number(),
    name: z.string(),
  })),
  production_companies: z.array(z.object({
    id: z.number(),
    name: z.string(),
    logo_path: z.string().nullable(),
    origin_country: z.string(),
  })),
  production_countries: z.array(z.object({
    iso_3166_1: z.string(),
    name: z.string(),
  })),
  spoken_languages: z.array(z.object({
    english_name: z.string(),
    iso_639_1: z.string(),
    name: z.string(),
  })),
  status: z.string(),
  tagline: z.string().nullable(),
  budget: z.number(),
  revenue: z.number(),
  homepage: z.string().nullable(),
  imdb_id: z.string().nullable(),
});

export type TmdbMovieDetails = z.infer<typeof TmdbMovieDetailsSchema>;

// TMDB 电视剧详情
export const TmdbTvDetailsSchema = z.object({
  id: z.number(),
  name: z.string(),
  original_name: z.string(),
  overview: z.string().nullable(),
  poster_path: z.string().nullable(),
  backdrop_path: z.string().nullable(),
  first_air_date: z.string(),
  last_air_date: z.string(),
  vote_average: z.number(),
  vote_count: z.number(),
  popularity: z.number(),
  adult: z.boolean(),
  original_language: z.string(),
  origin_country: z.array(z.string()),
  genres: z.array(z.object({
    id: z.number(),
    name: z.string(),
  })),
  created_by: z.array(z.object({
    id: z.number(),
    name: z.string(),
    gender: z.number().nullable(),
    profile_path: z.string().nullable(),
  })),
  episode_run_time: z.array(z.number()),
  homepage: z.string().nullable(),
  in_production: z.boolean(),
  languages: z.array(z.string()),
  last_episode_to_air: z.object({
    id: z.number(),
    name: z.string(),
    overview: z.string(),
    vote_average: z.number(),
    vote_count: z.number(),
    air_date: z.string(),
    episode_number: z.number(),
    production_code: z.string(),
    runtime: z.number().nullable(),
    season_number: z.number(),
    show_id: z.number(),
    still_path: z.string().nullable(),
  }).nullable(),
  next_episode_to_air: z.object({
    id: z.number(),
    name: z.string(),
    overview: z.string(),
    vote_average: z.number(),
    vote_count: z.number(),
    air_date: z.string(),
    episode_number: z.number(),
    production_code: z.string(),
    runtime: z.number().nullable(),
    season_number: z.number(),
    show_id: z.number(),
    still_path: z.string().nullable(),
  }).nullable(),
  networks: z.array(z.object({
    id: z.number(),
    logo_path: z.string().nullable(),
    name: z.string(),
    origin_country: z.string(),
  })),
  number_of_episodes: z.number(),
  number_of_seasons: z.number(),
  production_companies: z.array(z.object({
    id: z.number(),
    logo_path: z.string().nullable(),
    name: z.string(),
    origin_country: z.string(),
  })),
  production_countries: z.array(z.object({
    iso_3166_1: z.string(),
    name: z.string(),
  })),
  seasons: z.array(z.object({
    air_date: z.string().nullable(),
    episode_count: z.number(),
    id: z.number(),
    name: z.string(),
    overview: z.string(),
    poster_path: z.string().nullable(),
    season_number: z.number(),
    vote_average: z.number(),
  })),
  spoken_languages: z.array(z.object({
    english_name: z.string(),
    iso_639_1: z.string(),
    name: z.string(),
  })),
  status: z.string(),
  tagline: z.string(),
  type: z.string(),
});

export type TmdbTvDetails = z.infer<typeof TmdbTvDetailsSchema>;

// TMDB 季详情
export const TmdbSeasonDetailsSchema = z.object({
  _id: z.string(),
  air_date: z.string().nullable(),
  episodes: z.array(z.object({
    air_date: z.string().nullable(),
    episode_number: z.number(),
    episode_type: z.string(),
    id: z.number(),
    name: z.string(),
    overview: z.string(),
    production_code: z.string(),
    runtime: z.number().nullable(),
    season_number: z.number(),
    show_id: z.number(),
    still_path: z.string().nullable(),
    vote_average: z.number(),
    vote_count: z.number(),
    crew: z.array(z.any()).optional(),
    guest_stars: z.array(z.any()).optional(),
  })),
  name: z.string(),
  overview: z.string(),
  id: z.number(),
  poster_path: z.string().nullable(),
  season_number: z.number(),
  vote_average: z.number(),
});

export type TmdbSeasonDetails = z.infer<typeof TmdbSeasonDetailsSchema>;

// 缓存键类型
export interface TmdbCacheKey {
  search: string;
  media: { id: number; type: TmdbMediaType };
  season: { tvId: number; seasonNumber: number };
}

// 错误类型
export class TmdbError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'TmdbError';
  }
}

// TMDB 客户端配置
export interface TmdbClientConfig {
  apiKey: string;
  language?: AvailableLanguage;
  region?: string;
  baseURL?: string;
}
