// TMDB 缓存服务测试
import { PrismaClient } from '@prisma/client';
import { TmdbCacheService } from '../../../src/lib/tmdb/cache';
import { TmdbMediaType } from '../../../src/lib/tmdb/types';

// 模拟 Prisma 客户端
const mockPrisma = {
  tmdbSearchCache: {
    findUnique: jest.fn(),
    upsert: jest.fn(),
    delete: jest.fn(),
    deleteMany: jest.fn(),
    count: jest.fn(),
  },
  tmdbMediaCache: {
    findUnique: jest.fn(),
    upsert: jest.fn(),
    delete: jest.fn(),
    deleteMany: jest.fn(),
    count: jest.fn(),
  },
  tmdbSeasonCache: {
    findUnique: jest.fn(),
    upsert: jest.fn(),
    delete: jest.fn(),
    deleteMany: jest.fn(),
    count: jest.fn(),
  },
} as unknown as PrismaClient;

describe('TmdbCacheService', () => {
  let cacheService: TmdbCacheService;

  beforeEach(() => {
    cacheService = new TmdbCacheService(mockPrisma);
    jest.clearAllMocks();
  });

  describe('搜索缓存', () => {
    const mockSearchResponse = {
      page: 1,
      results: [
        {
          id: 603,
          media_type: 'movie' as const,
          title: 'The Matrix',
          overview: 'A computer hacker learns from mysterious rebels about the true nature of his reality.',
          poster_path: '/f89U3ADr1oiB1s9GkdPOEpXUk5H.jpg',
          release_date: '1999-03-30',
          vote_average: 8.2,
          vote_count: 24000,
        },
      ],
      total_pages: 1,
      total_results: 1,
    };

    it('应该返回缓存的搜索结果', async () => {
      const query = 'The Matrix';
      const mockCache = {
        query,
        data: JSON.stringify(mockSearchResponse),
        createdAt: new Date(),
      };

      (mockPrisma.tmdbSearchCache.findUnique as jest.Mock).mockResolvedValue(mockCache);

      const result = await cacheService.getSearchCache(query);

      expect(result).toEqual(mockSearchResponse);
      expect(mockPrisma.tmdbSearchCache.findUnique).toHaveBeenCalledWith({
        where: { query },
      });
    });

    it('应该在缓存不存在时返回 null', async () => {
      const query = 'Non-existent Movie';

      (mockPrisma.tmdbSearchCache.findUnique as jest.Mock).mockResolvedValue(null);

      const result = await cacheService.getSearchCache(query);

      expect(result).toBeNull();
    });

    it('应该在缓存过期时返回 null 并删除缓存', async () => {
      const query = 'Old Movie';
      const expiredDate = new Date(Date.now() - 8 * 24 * 60 * 60 * 1000); // 8天前
      const mockCache = {
        query,
        data: JSON.stringify(mockSearchResponse),
        createdAt: expiredDate,
      };

      (mockPrisma.tmdbSearchCache.findUnique as jest.Mock).mockResolvedValue(mockCache);
      (mockPrisma.tmdbSearchCache.delete as jest.Mock).mockResolvedValue({});

      const result = await cacheService.getSearchCache(query);

      expect(result).toBeNull();
      expect(mockPrisma.tmdbSearchCache.delete).toHaveBeenCalledWith({
        where: { query },
      });
    });

    it('应该保存搜索缓存', async () => {
      const query = 'The Matrix';

      (mockPrisma.tmdbSearchCache.upsert as jest.Mock).mockResolvedValue({});

      await cacheService.setSearchCache(query, mockSearchResponse);

      expect(mockPrisma.tmdbSearchCache.upsert).toHaveBeenCalledWith({
        where: { query },
        update: {
          data: JSON.stringify(mockSearchResponse),
          createdAt: expect.any(Date),
        },
        create: {
          query,
          data: JSON.stringify(mockSearchResponse),
        },
      });
    });
  });

  describe('媒体缓存', () => {
    const mockMovieDetails = {
      id: 603,
      title: 'The Matrix',
      original_title: 'The Matrix',
      overview: 'A computer hacker learns from mysterious rebels about the true nature of his reality.',
      poster_path: '/f89U3ADr1oiB1s9GkdPOEpXUk5H.jpg',
      backdrop_path: '/fNG7i7RqMErkcqhohV2a6cV1Ehy.jpg',
      release_date: '1999-03-30',
      runtime: 136,
      vote_average: 8.2,
      vote_count: 24000,
      popularity: 85.965,
      adult: false,
      video: false,
      original_language: 'en',
      genres: [{ id: 28, name: 'Action' }],
      production_companies: [],
      production_countries: [],
      spoken_languages: [],
      status: 'Released',
      tagline: 'Welcome to the Real World.',
      budget: 63000000,
      revenue: 467222824,
      homepage: '',
      imdb_id: 'tt0133093',
    };

    it('应该返回缓存的媒体详情', async () => {
      const tmdbId = 603;
      const mediaType = TmdbMediaType.MOVIE;
      const mockCache = {
        tmdbId,
        mediaType: 'MOVIE',
        data: JSON.stringify(mockMovieDetails),
        createdAt: new Date(),
      };

      (mockPrisma.tmdbMediaCache.findUnique as jest.Mock).mockResolvedValue(mockCache);

      const result = await cacheService.getMediaCache(tmdbId, mediaType);

      expect(result).toEqual(mockMovieDetails);
      expect(mockPrisma.tmdbMediaCache.findUnique).toHaveBeenCalledWith({
        where: { tmdbId },
      });
    });

    it('应该在媒体类型不匹配时返回 null', async () => {
      const tmdbId = 603;
      const mediaType = TmdbMediaType.TV;
      const mockCache = {
        tmdbId,
        mediaType: 'MOVIE', // 不匹配的类型
        data: JSON.stringify(mockMovieDetails),
        createdAt: new Date(),
      };

      (mockPrisma.tmdbMediaCache.findUnique as jest.Mock).mockResolvedValue(mockCache);

      const result = await cacheService.getMediaCache(tmdbId, mediaType);

      expect(result).toBeNull();
    });

    it('应该保存媒体缓存', async () => {
      const tmdbId = 603;
      const mediaType = TmdbMediaType.MOVIE;

      (mockPrisma.tmdbMediaCache.upsert as jest.Mock).mockResolvedValue({});

      await cacheService.setMediaCache(tmdbId, mediaType, mockMovieDetails);

      expect(mockPrisma.tmdbMediaCache.upsert).toHaveBeenCalledWith({
        where: { tmdbId },
        update: {
          mediaType: 'MOVIE',
          data: JSON.stringify(mockMovieDetails),
          createdAt: expect.any(Date),
        },
        create: {
          tmdbId,
          mediaType: 'MOVIE',
          data: JSON.stringify(mockMovieDetails),
        },
      });
    });
  });

  describe('季缓存', () => {
    const mockSeasonDetails = {
      _id: '5256c8a219c2956ff6046d47',
      air_date: '2008-01-20',
      episodes: [
        {
          air_date: '2008-01-20',
          episode_number: 1,
          id: 62085,
          name: 'Pilot',
          overview: 'Walter White, a struggling high school chemistry teacher...',
          production_code: '',
          runtime: 58,
          season_number: 1,
          show_id: 1396,
          still_path: '/9jCnjvzOdZrlEJcWZVPHNqJthkx.jpg',
          vote_average: 7.7,
          vote_count: 117,
          crew: [],
          guest_stars: [],
        },
      ],
      name: 'Season 1',
      overview: 'High school chemistry teacher Walter White...',
      id: 3572,
      poster_path: '/spMoPGxcCk2LTLBubSLZSsGbBvX.jpg',
      season_number: 1,
      vote_average: 8.2,
    };

    it('应该返回缓存的季详情', async () => {
      const tvId = 1396;
      const seasonNumber = 1;
      const mockCache = {
        tvId,
        seasonNumber,
        data: JSON.stringify(mockSeasonDetails),
        createdAt: new Date(),
      };

      (mockPrisma.tmdbSeasonCache.findUnique as jest.Mock).mockResolvedValue(mockCache);

      const result = await cacheService.getSeasonCache(tvId, seasonNumber);

      expect(result).toEqual(mockSeasonDetails);
      expect(mockPrisma.tmdbSeasonCache.findUnique).toHaveBeenCalledWith({
        where: {
          tvId_seasonNumber: {
            tvId,
            seasonNumber,
          },
        },
      });
    });

    it('应该保存季缓存', async () => {
      const tvId = 1396;
      const seasonNumber = 1;

      (mockPrisma.tmdbSeasonCache.upsert as jest.Mock).mockResolvedValue({});

      await cacheService.setSeasonCache(tvId, seasonNumber, mockSeasonDetails);

      expect(mockPrisma.tmdbSeasonCache.upsert).toHaveBeenCalledWith({
        where: {
          tvId_seasonNumber: {
            tvId,
            seasonNumber,
          },
        },
        update: {
          data: JSON.stringify(mockSeasonDetails),
          createdAt: expect.any(Date),
        },
        create: {
          tvId,
          seasonNumber,
          data: JSON.stringify(mockSeasonDetails),
        },
      });
    });
  });

  describe('缓存清理', () => {
    it('应该清理过期缓存', async () => {
      // 模拟统计过期季缓存数量
      (mockPrisma.tmdbSeasonCache.count as jest.Mock).mockResolvedValue(2);
      (mockPrisma.tmdbSearchCache.deleteMany as jest.Mock).mockResolvedValue({ count: 5 });
      (mockPrisma.tmdbMediaCache.deleteMany as jest.Mock).mockResolvedValue({ count: 3 });
      (mockPrisma.tmdbSeasonCache.deleteMany as jest.Mock).mockResolvedValue({ count: 0 }); // 剩余的孤立缓存

      await cacheService.cleanupExpiredCache();

      expect(mockPrisma.tmdbSeasonCache.count).toHaveBeenCalled();
      expect(mockPrisma.tmdbSearchCache.deleteMany).toHaveBeenCalled();
      expect(mockPrisma.tmdbMediaCache.deleteMany).toHaveBeenCalled();
      expect(mockPrisma.tmdbSeasonCache.deleteMany).toHaveBeenCalled();
    });
  });

  describe('缓存强制清除', () => {
    it('应该清除所有缓存', async () => {
      // 模拟统计总季缓存数量
      (mockPrisma.tmdbSeasonCache.count as jest.Mock).mockResolvedValue(3);
      (mockPrisma.tmdbSearchCache.deleteMany as jest.Mock).mockResolvedValue({ count: 10 });
      (mockPrisma.tmdbMediaCache.deleteMany as jest.Mock).mockResolvedValue({ count: 5 });
      (mockPrisma.tmdbSeasonCache.deleteMany as jest.Mock).mockResolvedValue({ count: 0 }); // 剩余的孤立缓存

      const result = await cacheService.clearAllCache();

      expect(result).toEqual({
        searchCount: 10,
        mediaCount: 5,
        seasonCount: 3, // 使用统计的总数
      });
      expect(mockPrisma.tmdbSeasonCache.count).toHaveBeenCalled();
      expect(mockPrisma.tmdbSearchCache.deleteMany).toHaveBeenCalledWith({});
      expect(mockPrisma.tmdbMediaCache.deleteMany).toHaveBeenCalledWith({});
      expect(mockPrisma.tmdbSeasonCache.deleteMany).toHaveBeenCalledWith({});
    });

    it('应该清除所有搜索缓存', async () => {
      (mockPrisma.tmdbSearchCache.deleteMany as jest.Mock).mockResolvedValue({ count: 8 });

      const result = await cacheService.clearSearchCache();

      expect(result).toBe(8);
      expect(mockPrisma.tmdbSearchCache.deleteMany).toHaveBeenCalledWith({});
    });

    it('应该清除所有媒体缓存', async () => {
      (mockPrisma.tmdbMediaCache.deleteMany as jest.Mock).mockResolvedValue({ count: 6 });

      const result = await cacheService.clearMediaCache();

      expect(result).toBe(6);
      expect(mockPrisma.tmdbMediaCache.deleteMany).toHaveBeenCalledWith({});
    });

    it('应该清除所有季缓存', async () => {
      (mockPrisma.tmdbSeasonCache.deleteMany as jest.Mock).mockResolvedValue({ count: 4 });

      const result = await cacheService.clearSeasonCache();

      expect(result).toBe(4);
      expect(mockPrisma.tmdbSeasonCache.deleteMany).toHaveBeenCalledWith({});
    });

    it('应该清除特定搜索缓存', async () => {
      const query = 'test query';
      (mockPrisma.tmdbSearchCache.deleteMany as jest.Mock).mockResolvedValue({ count: 1 });

      const result = await cacheService.clearSpecificSearchCache(query);

      expect(result).toBe(true);
      expect(mockPrisma.tmdbSearchCache.deleteMany).toHaveBeenCalledWith({
        where: { query },
      });
    });

    it('应该清除特定媒体缓存', async () => {
      const tmdbId = 123;
      // 模拟找到存在的缓存
      (mockPrisma.tmdbMediaCache.findUnique as jest.Mock).mockResolvedValue({
        tmdbId,
        mediaType: 'TV',
        data: '{}',
        createdAt: new Date(),
      });
      (mockPrisma.tmdbMediaCache.delete as jest.Mock).mockResolvedValue({});

      const result = await cacheService.clearSpecificMediaCache(tmdbId);

      expect(result).toBe(true);
      expect(mockPrisma.tmdbMediaCache.findUnique).toHaveBeenCalledWith({
        where: { tmdbId },
      });
      expect(mockPrisma.tmdbMediaCache.delete).toHaveBeenCalledWith({
        where: { tmdbId },
      });
    });

    it('应该在媒体缓存不存在时返回false', async () => {
      const tmdbId = 123;
      // 模拟缓存不存在
      (mockPrisma.tmdbMediaCache.findUnique as jest.Mock).mockResolvedValue(null);

      const result = await cacheService.clearSpecificMediaCache(tmdbId);

      expect(result).toBe(false);
      expect(mockPrisma.tmdbMediaCache.findUnique).toHaveBeenCalledWith({
        where: { tmdbId },
      });
      expect(mockPrisma.tmdbMediaCache.delete).not.toHaveBeenCalled();
    });

    it('应该清除特定季缓存', async () => {
      const tvId = 1396;
      const seasonNumber = 1;
      (mockPrisma.tmdbSeasonCache.deleteMany as jest.Mock).mockResolvedValue({ count: 1 });

      const result = await cacheService.clearSpecificSeasonCache(tvId, seasonNumber);

      expect(result).toBe(1);
      expect(mockPrisma.tmdbSeasonCache.deleteMany).toHaveBeenCalledWith({
        where: { tvId, seasonNumber },
      });
    });

    it('应该清除电视剧所有季缓存', async () => {
      const tvId = 1396;
      (mockPrisma.tmdbSeasonCache.deleteMany as jest.Mock).mockResolvedValue({ count: 3 });

      const result = await cacheService.clearSpecificSeasonCache(tvId);

      expect(result).toBe(3);
      expect(mockPrisma.tmdbSeasonCache.deleteMany).toHaveBeenCalledWith({
        where: { tvId },
      });
    });
  });
});
