# Seiri-chan - 功能需求文档

## 1. 项目概述

`Seiri-chan` 是一个基于Next.js构建的现代化Web应用，旨在为用户提供一个美观、直观、功能强大的动漫及媒体文件整理工具。它将继承现有Python项目 `Bangumi_Auto_rename` 的核心识别逻辑，并在此基础上进行扩展和优化，重点提升用户体验、批处理能力、AI识别的智能化和系统的健壮性。

## 2. 核心功能

### 2.1. 媒体文件整理
- **多类型支持**: 支持整理电视剧、电影、动画、动画电影四种类型的媒体文件。
- **智能识别**: 能够根据文件名和TMDB（The Movie Database）元数据，自动识别媒体信息。
- **标准重命名**: 将文件重命名为媒体服务器（如Jellyfin, Plex）偏好的 `SxxExx` 标准格式。
- **双引擎驱动**:
    - **传统引擎**: 基于高效的规则和算法，处理常规命名格式。
    - **AI引擎**: 利用大语言模型，处理复杂和模糊的命名情况，作为传统引擎的增强。

### 2.2. 任务管理
- **实时任务看板**: 在Web UI上实时展示所有任务的状态（等待、分析中、处理中、完成、失败）。
- **任务持久化**: 所有任务信息都将被持久化存储，方便追踪和管理。
- **精细化任务**: 任务分为两级：顶层的“媒体任务”和底层的“文件操作任务”，支持对单个失败的文件进行重试。
- **批量处理**: 支持一次性添加多个任务，包括多选路径和按深度扫描父目录。

### 2.3. 配置管理
- **Web UI配置**: 所有配置项均可通过Web界面进行管理。
- **多模式配置**: 为传统模式和AI模式提供独立的配置区域。
- **连接测试**: 提供一键测试TMDB和AI服务连接的功能。

## 3. 详细功能需求

### 3.1. 用户界面 (UI)
- **任务看板**:
    - 以卡片或列表形式展示所有“媒体任务”。
    - 显示任务名称、状态、进度条和关键信息。
    - 提供操作按钮：重试、删除、导出任务、查看详情/日志。
- **任务详情页**:
    - 点击任务卡片后，展示该任务包含的所有“文件操作任务”列表。
    - 显示每个文件的源路径、目标路径、处理状态。
    - 对处理失败的单个文件提供“重试”按钮。
- **添加任务向导**:
    - 支持选择任务类型（动漫、电视剧、电影）。
    - 提供服务器端文件浏览器供用户选择路径。
    - 支持多选路径。
    - 支持“批量添加”模式，并可设置扫描深度(扫描深度为1则代表将所选路径的所有一级子路径添加为一个独立的整理任务)。
- **设置页面**:
    - 使用Tab将配置项分为“通用”(tmdb、路径设置、默认文件处理模式)、“AI模式”、“其他”(可暂时留空)。
    - 路径配置项提供“浏览”按钮。
    - AI Prompt等大文本输入框使用体验更佳的代码编辑器组件。

### 3.2. 后端功能
#### 3.2.1. 任务管理
- **任务展开**: 用户提交的批量任务请求，在后端应被立即展开为多个独立的“媒体任务”记录。
- **状态追踪**: 任务生命周期状态应被准确记录和更新。
- **错误处理**: 任务执行失败时，应记录详细的错误信息。
- **任务导出**: 允许选择任务并导出。直接从数据库中读取媒体任务、文件任务和相关的TMDB缓存数据，组合成一个完整的、可复现的测试用例包。
- **任务重试**: 允许重试失败的整理任务和文件任务。

#### 3.2.2. 文件操作
- **多种整理方式**: 支持硬链接、软链接、复制、移动四种文件操作方式。
- **健壮的移动操作**: “移动”模式必须是事务性的。在操作失败时，系统必须尽可能保证源目录的完整性，并提供回滚或备份机制。
- **乐观的链接/复制**: 对于链接和复制操作，单个文件失败不应中断整个任务，记录错误后继续执行。

#### 3.2.3. AI 增强功能
- **前置分类**: 在调用AI前，先用传统标签（如`[OP]`, `[ED]`）对文件进行初步筛选和分类，去除无关文件并将标签作为额外信息提供给AI。
- **电影分离**: AI应能识别出混在剧集中但未在TMDB的剧集中列出的剧场版文件，并在分析结果中明确指出。系统随后应为这些文件自动创建新的“动画电影”整理任务。

#### 3.2.4. API 接口
- `POST /api/tasks`: 创建新任务（支持单个、多个、批量）。
- `GET /api/tasks`: 获取所有任务列表及其状态。
- `/api/tasks/ws`: WebSocket端点，用于实时推送任务状态更新。
- `POST /api/tasks/operation/{id}/retry`: 重试一个失败的“文件操作任务”。
- `GET /api/config`: 获取当前系统配置。
- `PUT /api/config`: 更新系统配置。
- `POST /api/ai/test`: 测试AI服务连接。
- `GET /api/utils/browse`: 提供文件系统浏览功能。

## 4. 非功能性需求
- **健壮性**: 关键操作（如文件移动）必须有回退机制或备份机制。API和服务需有完善的错误处理。
- **可扩展性**: 模块化设计，方便未来添加新的AI提供商或新的功能。
- **国际化 (i18n)**: UI文本应支持多语言，至少支持简体中文和英文。

## 5. 未来功能 (暂缓实现)
- **字幕整理**: 智能匹配并重命名字幕文件，支持`chs`/`cht`到`.简体中文.chi`/`.繁體中文.chi`的转换。
- **预告片整理**: 识别PV/Trailer文件并整理到`trailers`子目录。
- **字体文件收集**: 自动解压`[font]`压缩包到指定字体文件夹。