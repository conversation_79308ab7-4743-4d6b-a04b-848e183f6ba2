
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for tmdb</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> tmdb</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">35.64% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>108/303</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">22.78% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>18/79</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">37.2% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>16/43</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">35.76% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>108/302</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="cache.ts"><a href="cache.ts.html">cache.ts</a></td>
	<td data-value="70.83" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 70%"></div><div class="cover-empty" style="width: 30%"></div></div>
	</td>
	<td data-value="70.83" class="pct medium">70.83%</td>
	<td data-value="144" class="abs medium">102/144</td>
	<td data-value="72.72" class="pct medium">72.72%</td>
	<td data-value="22" class="abs medium">16/22</td>
	<td data-value="83.33" class="pct high">83.33%</td>
	<td data-value="18" class="abs high">15/18</td>
	<td data-value="70.83" class="pct medium">70.83%</td>
	<td data-value="144" class="abs medium">102/144</td>
	</tr>

<tr>
	<td class="file low" data-value="client.ts"><a href="client.ts.html">client.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="106" class="abs low">0/106</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="39" class="abs low">0/39</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="15" class="abs low">0/15</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="105" class="abs low">0/105</td>
	</tr>

<tr>
	<td class="file low" data-value="index.ts"><a href="index.ts.html">index.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="42" class="abs low">0/42</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="16" class="abs low">0/16</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="42" class="abs low">0/42</td>
	</tr>

<tr>
	<td class="file medium" data-value="types.ts"><a href="types.ts.html">types.ts</a></td>
	<td data-value="54.54" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 54%"></div><div class="cover-empty" style="width: 46%"></div></div>
	</td>
	<td data-value="54.54" class="pct medium">54.54%</td>
	<td data-value="11" class="abs medium">6/11</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="2" class="abs medium">1/2</td>
	<td data-value="54.54" class="pct medium">54.54%</td>
	<td data-value="11" class="abs medium">6/11</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-03T05:19:54.837Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    