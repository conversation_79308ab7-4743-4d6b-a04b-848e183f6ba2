// 配置管理器测试
import { join } from 'path';
import { existsSync, writeFileSync, unlinkSync } from 'fs';
import { ConfigManager } from '@/lib/config/manager';
import { DEFAULT_CONFIG, Config } from '@/lib/config/types';

describe('ConfigManager', () => {
  let configManager: ConfigManager;
  let testConfigPath: string;
  let originalCwd: string;

  beforeEach(() => {
    // 保存原始工作目录
    originalCwd = process.cwd();
    
    // 创建测试配置文件路径
    testConfigPath = join(process.cwd(), '__tests__', 'temp', 'seiri.toml');
    
    // 模拟 process.cwd() 返回测试目录
    jest.spyOn(process, 'cwd').mockReturnValue(join(process.cwd(), '__tests__', 'temp'));
    
    // 清理可能存在的测试配置文件
    if (existsSync(testConfigPath)) {
      unlinkSync(testConfigPath);
    }
    
    // 重置 ConfigManager 单例
    (ConfigManager as any).instance = undefined;
    configManager = ConfigManager.getInstance();
  });

  afterEach(() => {
    // 清理测试配置文件
    if (existsSync(testConfigPath)) {
      unlinkSync(testConfigPath);
    }
    
    // 恢复原始工作目录
    jest.restoreAllMocks();
  });

  describe('getInstance', () => {
    it('应该返回单例实例', () => {
      const instance1 = ConfigManager.getInstance();
      const instance2 = ConfigManager.getInstance();
      
      expect(instance1).toBe(instance2);
      expect(instance1).toBeInstanceOf(ConfigManager);
    });
  });

  describe('getConfig', () => {
    it('配置文件不存在时应该返回默认配置', () => {
      const config = configManager.getConfig();
      
      expect(config).toEqual(DEFAULT_CONFIG);
    });

    it('应该正确加载有效的配置文件', async () => {
      // 创建测试配置文件
      const testConfig = `
[general]
[general.tmdb]
apiKey = "test_api_key"
language = "en-US"

[general.paths]
outputRoot = "/test/output"

[general.defaults]
enableAI = true
`;
      
      writeFileSync(testConfigPath, testConfig, 'utf-8');
      
      // 重新加载配置
      const config = configManager.reloadConfig();
      
      expect(config.general.tmdb.apiKey).toBe('test_api_key');
      expect(config.general.tmdb.language).toBe('en-US');
      expect(config.general.paths.outputRoot).toBe('/test/output');
      expect(config.general.defaults.enableAI).toBe(true);
    });
  });

  describe('updateConfig', () => {
    it('应该正确更新配置', async () => {
      const updateData = {
        general: {
          tmdb: {
            apiKey: 'new_api_key',
          },
          paths: {
            outputRoot: '/test/output',
          },
          defaults: {
            enableAI: true,
          },
        },
      };

      await configManager.updateConfig(updateData);

      const config = configManager.getConfig();
      expect(config.general.tmdb.apiKey).toBe('new_api_key');
      expect(config.general.paths.outputRoot).toBe('/test/output');
      expect(config.general.defaults.enableAI).toBe(true);
      // 其他配置应该保持默认值
      expect(config.general.tmdb.language).toBe('zh-CN');
    });

    it('应该验证配置格式', async () => {
      const invalidConfig = {
        general: {
          tmdb: {
            apiKey: 123, // 应该是字符串
          },
        },
      };

      await expect(configManager.updateConfig(invalidConfig as any))
        .rejects
        .toThrow();
    });
  });

  describe('configExists', () => {
    it('配置文件不存在时应该返回 false', () => {
      expect(configManager.configExists()).toBe(false);
    });

    it('配置文件存在时应该返回 true', async () => {
      await configManager.createDefaultConfig();
      expect(configManager.configExists()).toBe(true);
    });
  });

  describe('createDefaultConfig', () => {
    it('应该创建默认配置文件', async () => {
      await configManager.createDefaultConfig();
      
      expect(existsSync(testConfigPath)).toBe(true);
      
      const config = configManager.getConfig();
      expect(config).toEqual(DEFAULT_CONFIG);
    });
  });

  describe('validateConfig', () => {
    it('应该验证有效配置', () => {
      const validConfig = {
        ...DEFAULT_CONFIG,
        general: {
          ...DEFAULT_CONFIG.general,
          tmdb: {
            ...DEFAULT_CONFIG.general.tmdb,
            apiKey: 'test_api_key',
          },
          paths: {
            ...DEFAULT_CONFIG.general.paths,
            outputRoot: '/test/output',
          },
        },
      };

      expect(() => configManager.validateConfig(validConfig)).not.toThrow();
    });

    it('应该拒绝无效配置', () => {
      const invalidConfig = {
        general: {
          tmdb: {
            apiKey: 123, // 应该是字符串
          },
        },
      };

      expect(() => configManager.validateConfig(invalidConfig))
        .toThrow();
    });
  });

  describe('getGeneralConfig', () => {
    it('应该返回通用配置', () => {
      const generalConfig = configManager.getGeneralConfig();
      
      expect(generalConfig).toEqual(DEFAULT_CONFIG.general);
    });
  });

  describe('getAIConfig', () => {
    it('应该返回 AI 配置', () => {
      const aiConfig = configManager.getAIConfig();
      
      expect(aiConfig).toEqual(DEFAULT_CONFIG.ai);
    });
  });

  describe('getOtherConfig', () => {
    it('应该返回其他配置', () => {
      const otherConfig = configManager.getOtherConfig();
      
      expect(otherConfig).toEqual(DEFAULT_CONFIG.other);
    });
  });
});
