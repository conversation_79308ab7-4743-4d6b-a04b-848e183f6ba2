# 单元 2.1: TMDB 客户端与缓存 - 完成文档

## 概述

本单元实现了 TMDB (The Movie Database) 客户端与缓存系统，为后续的媒体识别功能提供基础支持。

## 实现内容

### 1. 核心模块

#### 1.1 类型定义 (`src/lib/tmdb/types.ts`)
- **TmdbMediaType**: 媒体类型枚举 (MOVIE, TV)
- **TmdbSearchResultItem**: 搜索结果项类型
- **TmdbSearchResponse**: 搜索响应类型
- **TmdbMovieDetails**: 电影详情类型
- **TmdbTvDetails**: 电视剧详情类型
- **TmdbSeasonDetails**: 季详情类型
- **TmdbError**: 自定义错误类型
- **TmdbClientConfig**: 客户端配置类型

#### 1.2 缓存服务 (`src/lib/tmdb/cache.ts`)
- **TmdbCacheService**: 缓存管理服务
  - `getSearchCache()`: 获取搜索结果缓存
  - `setSearchCache()`: 设置搜索结果缓存
  - `getMediaCache()`: 获取媒体详情缓存
  - `setMediaCache()`: 设置媒体详情缓存
  - `getSeasonCache()`: 获取季详情缓存
  - `setSeasonCache()`: 设置季详情缓存
  - `cleanupExpiredCache()`: 清理过期缓存
  - `clearAllCache()`: 强制清除所有缓存
  - `clearSearchCache()`: 强制清除所有搜索缓存
  - `clearMediaCache()`: 强制清除所有媒体缓存
  - `clearSeasonCache()`: 强制清除所有季缓存
  - `clearSpecificSearchCache()`: 清除特定搜索缓存
  - `clearSpecificMediaCache()`: 清除特定媒体缓存
  - `clearSpecificSeasonCache()`: 清除特定季缓存

#### 1.3 TMDB 客户端 (`src/lib/tmdb/client.ts`)
- **TmdbClient**: 主要客户端类
  - `searchMulti()`: 搜索媒体（电影和电视剧）- 支持 `skipCache` 参数
  - `getMovieDetails()`: 获取电影详情 - 支持 `skipCache` 参数
  - `getTvDetails()`: 获取电视剧详情 - 支持 `skipCache` 参数
  - `getSeasonDetails()`: 获取电视剧季详情 - 支持 `skipCache` 参数
  - `testConnection()`: 测试 TMDB 连接
  - `cleanupCache()`: 清理过期缓存
  - `clearAllCache()`: 清除所有缓存
  - `clearSearchCache()`: 清除所有搜索缓存
  - `clearMediaCache()`: 清除所有媒体缓存
  - `clearSeasonCache()`: 清除所有季缓存
  - `clearSpecificSearchCache()`: 清除特定搜索缓存
  - `clearSpecificMediaCache()`: 清除特定媒体缓存
  - `clearSpecificSeasonCache()`: 清除特定季缓存
  - `updateConfig()`: 更新配置

#### 1.4 模块入口 (`src/lib/tmdb/index.ts`)
- 导出所有类型和类
- 提供单例模式的客户端获取函数
- 提供配置更新和重置功能

### 2. 缓存策略

#### 2.1 缓存类型与过期时间
- **搜索缓存**: 7天过期
- **媒体详情缓存**: 30天过期
- **季详情缓存**: 30天过期

#### 2.2 缓存键设计
- **搜索缓存**: `multi:{query}:{page}:{language}:{region}`
- **媒体缓存**: 使用 TMDB ID 作为主键
- **季缓存**: 使用 TV ID + 季号的复合主键

#### 2.3 缓存清理
- 自动检测过期缓存并异步删除
- 提供手动清理接口

#### 2.4 缓存强制清除
- 支持清除所有类型缓存或特定类型缓存
- 支持清除特定缓存项（按查询关键词、TMDB ID 等）
- 返回清除的记录数量便于监控

#### 2.5 跳过缓存功能
- 所有查询方法支持 `skipCache` 参数
- 跳过缓存读取但仍保存到缓存
- 适用于需要获取最新数据的场景

#### 2.6 级联删除功能
- 季缓存与电视剧媒体缓存建立外键关系
- 删除电视剧媒体缓存时自动级联删除相关季缓存
- 确保数据一致性，避免孤立的季缓存记录
- 优化缓存清理性能，减少数据库操作

### 3. 错误处理

#### 3.1 自定义错误类型
```typescript
class TmdbError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number,
    public originalError?: Error
  )
}
```

#### 3.2 错误代码
- `SEARCH_ERROR`: 搜索失败
- `MOVIE_DETAILS_ERROR`: 获取电影详情失败
- `TV_DETAILS_ERROR`: 获取电视剧详情失败
- `SEASON_DETAILS_ERROR`: 获取季详情失败
- `CACHE_SAVE_ERROR`: 缓存保存失败
- `CACHE_CLEANUP_ERROR`: 缓存清理失败

### 4. 数据验证

使用 Zod 进行严格的数据验证：
- 验证 TMDB API 响应数据结构
- 确保类型安全
- 防止无效数据进入系统

#### 4.1 TMDB 语言类型验证

集成了 tmdb-ts 的 `AvailableLanguage` 类型，提供严格的语言验证：

```typescript
// 从 tmdb-ts 导入严格的语言类型
import type { AvailableLanguage } from 'tmdb-ts';

// 在配置中使用 Zod 枚举验证
export const TmdbLanguageSchema = z.enum(TMDB_LANGUAGES);
export type TmdbLanguage = z.infer<typeof TmdbLanguageSchema>;

// 客户端配置使用严格类型
export interface TmdbClientConfig {
  apiKey: string;
  language?: AvailableLanguage;
  region?: string;
  baseURL?: string;
}
```

**支持的语言包括**：
- 中文：`zh-CN`, `zh-HK`, `zh-SG`, `zh-TW`
- 英文：`en-US`, `en-GB`, `en-CA`, `en-AU` 等
- 其他语言：`ja-JP`, `ko-KR`, `fr-FR`, `de-DE`, `es-ES` 等
- 总计 125+ 种语言和地区组合

**优势**：
- 编译时类型检查，防止无效语言代码
- IDE 自动补全支持
- 与 TMDB API 完全兼容
- 配置验证时提供明确的错误信息

### 5. 日志记录

集成了完整的日志系统：
- 调试级别：API 调用详情、缓存命中/未命中
- 信息级别：连接测试结果、缓存清理统计
- 警告级别：缓存过期、连接异常
- 错误级别：API 调用失败、缓存操作失败

## 测试覆盖

### 1. 缓存服务测试 (`__tests__/lib/tmdb/cache.test.ts`)
- ✅ 搜索缓存的获取和设置
- ✅ 媒体缓存的获取和设置
- ✅ 季缓存的获取和设置
- ✅ 缓存过期检测和清理
- ✅ 媒体类型匹配验证
- ✅ 强制清除所有缓存
- ✅ 强制清除特定类型缓存
- ✅ 清除特定缓存项

### 2. 客户端测试 (`__tests__/lib/tmdb/client.test.ts`)
- ✅ 搜索功能（缓存命中/未命中）
- ✅ 电影详情获取
- ✅ 电视剧详情获取
- ✅ 连接测试（成功/失败场景）
- ✅ 配置更新
- ✅ 错误处理
- ✅ skipCache 参数功能测试
- ✅ 缓存清除功能测试

## 配置集成

与现有配置系统完全集成：
- 从 `config.general.tmdb` 读取配置
- 支持 API Key、语言、区域设置
- 支持运行时配置更新

### 配置类型增强

更新了配置系统以支持严格的 TMDB 语言验证：

```typescript
// 配置 Schema 中的 TMDB 部分
tmdb: z.object({
  apiKey: z.string().min(1, 'TMDB API Key 不能为空'),
  language: TmdbLanguageSchema.default('zh-CN'), // 严格的语言验证
  region: z.string().default('CN'),
}),
```

**配置验证优势**：
- 在配置加载时验证语言代码有效性
- 提供清晰的错误信息
- 防止运行时 API 调用失败
- 支持配置文件和 UI 表单验证

## 性能优化

### 1. 缓存优化
- 智能缓存策略减少 API 调用
- 异步缓存清理不阻塞主流程
- 缓存命中率日志便于监控

### 2. 错误恢复
- 缓存操作失败不影响主功能
- 优雅降级：缓存失败时直接调用 API
- 详细错误信息便于调试

## 使用示例

```typescript
import { getTmdbClient, updateTmdbClientConfig, clearAllTmdbCache } from '@/lib/tmdb';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();
const tmdbClient = getTmdbClient(prisma);

// 搜索媒体
const searchResults = await tmdbClient.searchMulti('The Matrix');

// 跳过缓存搜索（获取最新数据）
const freshSearchResults = await tmdbClient.searchMulti('The Matrix', 1, true);

// 获取电影详情
const movieDetails = await tmdbClient.getMovieDetails(603);

// 跳过缓存获取电影详情
const freshMovieDetails = await tmdbClient.getMovieDetails(603, true);

// 获取电视剧详情
const tvDetails = await tmdbClient.getTvDetails(1396);

// 跳过缓存获取电视剧详情
const freshTvDetails = await tmdbClient.getTvDetails(1396, true);

// 获取季详情
const seasonDetails = await tmdbClient.getSeasonDetails(1396, 1);

// 跳过缓存获取季详情
const freshSeasonDetails = await tmdbClient.getSeasonDetails(1396, 1, true);

// 测试连接
const connectionTest = await tmdbClient.testConnection();

// 更新配置（使用严格的语言类型）
updateTmdbClientConfig({
  language: 'en-US', // TypeScript 会验证这是有效的 TMDB 语言代码
  region: 'US',
});

// 清理过期缓存
await tmdbClient.cleanupCache();

// 强制清除所有缓存
const clearResult = await tmdbClient.clearAllCache();
console.log(`清除缓存: 搜索 ${clearResult.searchCount} 条, 媒体 ${clearResult.mediaCount} 条, 季 ${clearResult.seasonCount} 条`);

// 清除特定类型缓存
const searchCacheCount = await tmdbClient.clearSearchCache();
const mediaCacheCount = await tmdbClient.clearMediaCache();
const seasonCacheCount = await tmdbClient.clearSeasonCache();

// 清除特定缓存项
const searchCleared = await tmdbClient.clearSpecificSearchCache('multi:The Matrix:1:zh-CN:CN');
const mediaCleared = await tmdbClient.clearSpecificMediaCache(603);
const seasonCleared = await tmdbClient.clearSpecificSeasonCache(1396, 1);

// 使用全局便捷函数
const globalClearResult = await clearAllTmdbCache();
```

### 配置验证示例

```typescript
import { TmdbLanguageSchema } from '@/lib/config/types';

// 验证语言代码
const validLanguage = TmdbLanguageSchema.parse('zh-CN'); // ✅ 通过
const invalidLanguage = TmdbLanguageSchema.parse('invalid'); // ❌ 抛出错误

// 在配置更新时自动验证
const config = {
  language: 'ja-JP' as const, // TypeScript 编译时检查
  region: 'JP',
};
```

## 下一步

单元 2.1 已完成，为后续单元提供了：
1. 完整的 TMDB API 封装
2. 高效的缓存系统
3. 类型安全的数据结构
4. 完善的错误处理
5. 全面的测试覆盖

可以继续进行单元 2.2: 传统识别引擎的开发。

## 文件清单

### 源代码文件
- `src/lib/tmdb/types.ts` - 类型定义（包含 TMDB 语言类型集成）
- `src/lib/tmdb/cache.ts` - 缓存服务
- `src/lib/tmdb/client.ts` - TMDB 客户端（支持严格语言类型）
- `src/lib/tmdb/index.ts` - 模块入口

### 配置文件更新
- `src/lib/config/types.ts` - 增强了 TMDB 语言验证

### 测试文件
- `__tests__/lib/tmdb/cache.test.ts` - 缓存服务测试
- `__tests__/lib/tmdb/client.test.ts` - 客户端测试（包含语言类型测试）

### 文档文件
- `docs/unit-2.1-tmdb-client-cache.md` - 本文档

### 主要改进
1. **类型安全增强**: 集成 tmdb-ts 的 `AvailableLanguage` 类型
2. **配置验证**: 使用 Zod 枚举验证 TMDB 语言代码
3. **编译时检查**: TypeScript 在编译时验证语言代码有效性
4. **API 调用优化**: 移除不必要的类型转换，使用原生类型
5. **缓存强制清除**: 支持清除所有缓存或特定类型缓存
6. **跳过缓存功能**: 支持跳过缓存直接获取最新数据
7. **精细化缓存控制**: 支持清除特定缓存项

### 新增功能详情

#### 缓存强制清除功能
- **clearAllCache()**: 一次性清除所有类型缓存，返回详细统计
- **clearSearchCache()**: 清除所有搜索缓存
- **clearMediaCache()**: 清除所有媒体详情缓存
- **clearSeasonCache()**: 清除所有季详情缓存
- **clearSpecificSearchCache()**: 按查询关键词清除特定搜索缓存
- **clearSpecificMediaCache()**: 按 TMDB ID 清除特定媒体缓存
- **clearSpecificSeasonCache()**: 按电视剧 ID 和季号清除特定季缓存

#### 跳过缓存功能
- 所有查询方法新增 `skipCache` 可选参数（默认 false）
- 跳过缓存读取但仍保存到缓存，确保下次调用可以使用缓存
- 适用于需要获取最新数据的场景，如用户手动刷新

#### 全局便捷函数
- **clearAllTmdbCache()**: 全局清除所有缓存
- **clearTmdbSearchCache()**: 全局清除搜索缓存
- **clearTmdbMediaCache()**: 全局清除媒体缓存
- **clearTmdbSeasonCache()**: 全局清除季缓存
- **cleanupTmdbCache()**: 全局清理过期缓存

#### 级联删除功能
- **数据库级别的级联删除**: 在Prisma schema中定义了外键关系
- **自动清理相关数据**: 删除电视剧媒体缓存时自动删除所有相关季缓存
- **智能统计**: 缓存清理方法会正确统计被级联删除的记录数量
- **性能优化**: 减少了手动清理孤立季缓存的需要

