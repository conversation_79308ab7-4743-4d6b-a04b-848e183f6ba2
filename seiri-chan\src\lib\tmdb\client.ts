// TMDB 客户端
import { TMDB, SeasonSelection } from 'tmdb-ts';
import { PrismaClient } from '@prisma/client';
import { logger } from '../logger';
import { TmdbCacheService } from './cache';
import {
  TmdbClientConfig,
  TmdbSearchResponse,
  TmdbMovieDetails,
  TmdbTvDetails,
  TmdbSeasonDetails,
  TmdbMediaType,
  TmdbError,
} from './types';

export class TmdbClient {
  private tmdb: TMDB;
  private cache: TmdbCacheService;
  private config: TmdbClientConfig;

  constructor(config: TmdbClientConfig, prisma: PrismaClient) {
    this.config = config;
    this.tmdb = new TMDB(config.apiKey);
    this.cache = new TmdbCacheService(prisma);

    logger.info('TMDB 客户端已初始化');
  }

  /**
   * 搜索媒体（电影和电视剧）- 支持多页拼接
   * @param query 搜索关键词
   * @param maxPages 最大搜索页数，默认为3
   * @param skipCache 是否跳过缓存
   * @returns 拼接后的搜索结果
   */
  async searchMulti(query: string, maxPages: number = 3, skipCache: boolean = false): Promise<TmdbSearchResponse> {
    try {
      // 构建缓存键（包含最大页数）
      const cacheKey = `multi:${query}:maxPages${maxPages}:${this.config.language}:${this.config.region}`;

      // 尝试从缓存获取（除非跳过缓存）
      if (!skipCache) {
        const cached = await this.cache.getSearchCache(cacheKey);
        if (cached) {
          return cached;
        }
      } else {
        logger.debug(`跳过缓存，直接搜索: ${query} (最大页数: ${maxPages})`);
      }

      logger.debug(`搜索媒体: ${query} (最大页数: ${maxPages})`);

      // 获取第一页数据
      const firstPageResponse = await this.tmdb.search.multi({
        query,
        page: 1,
        language: this.config.language,
      });

      // 初始化结果
      let allResults = [...firstPageResponse.results];
      const totalPages = Math.min(firstPageResponse.total_pages, maxPages);

      // 如果有多页且需要获取更多页面
      if (totalPages > 1) {
        const pagePromises: Promise<any>[] = [];

        for (let page = 2; page <= totalPages; page++) {
          pagePromises.push(
            this.tmdb.search.multi({
              query,
              page,
              language: this.config.language,
            })
          );
        }

        // 并行获取其他页面
        const otherPagesResponses = await Promise.all(pagePromises);

        // 拼接所有结果
        for (const response of otherPagesResponses) {
          allResults.push(...response.results);
        }
      }

      // 构建最终响应
      const finalResponse: TmdbSearchResponse = {
        page: 1, // 拼接后的结果统一为第1页
        results: allResults,
        total_pages: 1, // 拼接后只有1页
        total_results: allResults.length,
      };

      // 缓存结果
      await this.cache.setSearchCache(cacheKey, finalResponse);

      logger.debug(`搜索完成: ${query}, 拼接了 ${totalPages} 页，共找到 ${allResults.length} 个结果`);
      return finalResponse;
    } catch (error) {
      logger.error(`搜索媒体失败: ${query}`, error);

      if (error instanceof Error) {
        throw new TmdbError(
          `搜索媒体失败: ${error.message}`,
          'SEARCH_ERROR',
          undefined,
          error
        );
      }

      throw new TmdbError('搜索媒体失败: 未知错误', 'SEARCH_ERROR');
    }
  }

  /**
   * 获取电影详情
   */
  async getMovieDetails(movieId: number, skipCache: boolean = false): Promise<TmdbMovieDetails> {
    try {
      // 尝试从缓存获取（除非跳过缓存）
      if (!skipCache) {
        const cached = await this.cache.getMediaCache(movieId, TmdbMediaType.MOVIE);
        if (cached) {
          return cached as TmdbMovieDetails;
        }
      } else {
        logger.debug(`跳过缓存，直接获取电影详情: ${movieId}`);
      }

      logger.debug(`获取电影详情: ${movieId}`);

      // 调用 TMDB API
      const response = await this.tmdb.movies.details(movieId);

      // 缓存结果
      await this.cache.setMediaCache(movieId, TmdbMediaType.MOVIE, response);

      logger.debug(`电影详情获取完成: ${response.title} (${movieId})`);
      return response;
    } catch (error) {
      logger.error(`获取电影详情失败: ${movieId}`, error);

      if (error instanceof Error) {
        throw new TmdbError(
          `获取电影详情失败: ${error.message}`,
          'MOVIE_DETAILS_ERROR',
          undefined,
          error
        );
      }

      throw new TmdbError('获取电影详情失败: 未知错误', 'MOVIE_DETAILS_ERROR');
    }
  }

  /**
   * 获取电视剧详情
   */
  async getTvDetails(tvId: number, skipCache: boolean = false): Promise<TmdbTvDetails> {
    try {
      // 尝试从缓存获取（除非跳过缓存）
      if (!skipCache) {
        const cached = await this.cache.getMediaCache(tvId, TmdbMediaType.TV);
        if (cached) {
          return cached as TmdbTvDetails;
        }
      } else {
        logger.debug(`跳过缓存，直接获取电视剧详情: ${tvId}`);
      }

      logger.debug(`获取电视剧详情: ${tvId}`);

      // 调用 TMDB API
      const response = await this.tmdb.tvShows.details(tvId);

      // 缓存结果
      await this.cache.setMediaCache(tvId, TmdbMediaType.TV, response);

      logger.debug(`电视剧详情获取完成: ${response.name} (${tvId})`);
      return response;
    } catch (error) {
      logger.error(`获取电视剧详情失败: ${tvId}`, error);

      if (error instanceof Error) {
        throw new TmdbError(
          `获取电视剧详情失败: ${error.message}`,
          'TV_DETAILS_ERROR',
          undefined,
          error
        );
      }

      throw new TmdbError('获取电视剧详情失败: 未知错误', 'TV_DETAILS_ERROR');
    }
  }

  /**
   * 获取电视剧季详情
   */
  async getSeasonDetails(tvId: number, seasonNumber: number, skipCache: boolean = false): Promise<TmdbSeasonDetails> {
    try {
      // 尝试从缓存获取（除非跳过缓存）
      if (!skipCache) {
        const cached = await this.cache.getSeasonCache(tvId, seasonNumber);
        if (cached) {
          return cached;
        }
      } else {
        logger.debug(`跳过缓存，直接获取季详情: TV${tvId} S${seasonNumber}`);
      }

      logger.debug(`获取季详情: TV${tvId} S${seasonNumber}`);

      // 调用 TMDB API
      const response = await this.tmdb.tvSeasons.details(
        {
          tvShowID: tvId,
          seasonNumber: seasonNumber
        } as SeasonSelection,
        undefined,
        {
          language: this.config.language,
        }
      );

      // 缓存结果
      await this.cache.setSeasonCache(tvId, seasonNumber, response);

      logger.debug(`季详情获取完成: ${response.name} (TV${tvId} S${seasonNumber})`);
      return response;
    } catch (error) {
      logger.error(`获取季详情失败: TV${tvId} S${seasonNumber}`, error);

      if (error instanceof Error) {
        throw new TmdbError(
          `获取季详情失败: ${error.message}`,
          'SEASON_DETAILS_ERROR',
          undefined,
          error
        );
      }

      throw new TmdbError('获取季详情失败: 未知错误', 'SEASON_DETAILS_ERROR');
    }
  }

  /**
   * 测试 TMDB 连接
   */
  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      logger.debug('测试 TMDB 连接');

      // 获取账户信息来判断TMDB连接情况
      const response = await this.tmdb.account.details()

      if (response && response.id > 0) {
        logger.info('TMDB 连接测试成功');
        return {
          success: true,
          message: 'TMDB 连接正常',
        };
      } else {
        logger.warn('TMDB 连接测试失败: 未返回预期结果');
        return {
          success: false,
          message: 'TMDB 连接异常: 未返回预期结果',
        };
      }
    } catch (error) {
      logger.error('TMDB 连接测试失败', error);
      
      let message = 'TMDB 连接失败';
      if (error instanceof Error) {
        message += `: ${error.message}`;
      }
      
      return {
        success: false,
        message,
      };
    }
  }

  /**
   * 清理过期缓存
   */
  async cleanupCache(): Promise<void> {
    await this.cache.cleanupExpiredCache();
  }

  /**
   * 清除所有缓存
   */
  async clearAllCache(): Promise<{ searchCount: number; mediaCount: number; seasonCount: number }> {
    return await this.cache.clearAllCache();
  }

  /**
   * 清除所有搜索缓存
   */
  async clearSearchCache(): Promise<number> {
    return await this.cache.clearSearchCache();
  }

  /**
   * 清除所有媒体缓存
   */
  async clearMediaCache(): Promise<number> {
    return await this.cache.clearMediaCache();
  }

  /**
   * 清除所有季缓存
   */
  async clearSeasonCache(): Promise<number> {
    return await this.cache.clearSeasonCache();
  }

  /**
   * 清除特定搜索缓存
   */
  async clearSpecificSearchCache(query: string): Promise<boolean> {
    return await this.cache.clearSpecificSearchCache(query);
  }

  /**
   * 清除特定媒体缓存
   */
  async clearSpecificMediaCache(tmdbId: number): Promise<boolean> {
    return await this.cache.clearSpecificMediaCache(tmdbId);
  }

  /**
   * 清除特定季缓存
   */
  async clearSpecificSeasonCache(tvId: number, seasonNumber?: number): Promise<number> {
    return await this.cache.clearSpecificSeasonCache(tvId, seasonNumber);
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<TmdbClientConfig>): void {
    this.config = { ...this.config, ...config };

    // 如果 API Key 发生变化，重新初始化 TMDB 客户端
    if (config.apiKey) {
      this.tmdb = new TMDB(config.apiKey);
      logger.info('TMDB 客户端配置已更新');
    }
  }
}
