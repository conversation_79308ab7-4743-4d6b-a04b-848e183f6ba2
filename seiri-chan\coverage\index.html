
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">14.4% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>85/590</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">7.52% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>14/186</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">14.78% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>17/115</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">14.86% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>84/565</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="config"><a href="config/index.html">config</a></td>
	<td data-value="64.7" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 64%"></div><div class="cover-empty" style="width: 36%"></div></div>
	</td>
	<td data-value="64.7" class="pct medium">64.7%</td>
	<td data-value="102" class="abs medium">66/102</td>
	<td data-value="70.58" class="pct medium">70.58%</td>
	<td data-value="17" class="abs medium">12/17</td>
	<td data-value="60.86" class="pct medium">60.86%</td>
	<td data-value="23" class="abs medium">14/23</td>
	<td data-value="77.64" class="pct medium">77.64%</td>
	<td data-value="85" class="abs medium">66/85</td>
	</tr>

<tr>
	<td class="file low" data-value="errors"><a href="errors/index.html">errors</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="83" class="abs low">0/83</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="44" class="abs low">0/44</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="19" class="abs low">0/19</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="82" class="abs low">0/82</td>
	</tr>

<tr>
	<td class="file medium" data-value="logger"><a href="logger/index.html">logger</a></td>
	<td data-value="55.88" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 55%"></div><div class="cover-empty" style="width: 45%"></div></div>
	</td>
	<td data-value="55.88" class="pct medium">55.88%</td>
	<td data-value="34" class="abs medium">19/34</td>
	<td data-value="12.5" class="pct low">12.5%</td>
	<td data-value="16" class="abs low">2/16</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="9" class="abs low">3/9</td>
	<td data-value="54.54" class="pct medium">54.54%</td>
	<td data-value="33" class="abs medium">18/33</td>
	</tr>

<tr>
	<td class="file low" data-value="tmdb"><a href="tmdb/index.html">tmdb</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="294" class="abs low">0/294</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="77" class="abs low">0/77</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="43" class="abs low">0/43</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="294" class="abs low">0/294</td>
	</tr>

<tr>
	<td class="file low" data-value="utils"><a href="utils/index.html">utils</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="77" class="abs low">0/77</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="32" class="abs low">0/32</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="21" class="abs low">0/21</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="71" class="abs low">0/71</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-03T05:07:50.265Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    